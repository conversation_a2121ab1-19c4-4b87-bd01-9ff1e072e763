version: "3"
services:
  temizlik-delisi-db:
    image: "postgis/postgis:14-3.3"
    container_name:  temizlik-delisi-db
    volumes:
      -  temizlik_delisi_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "127.0.0.1:5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}

  temizlik-delisi:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image:  temizlik-delisi
    container_name:  temizlik-delisi
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
      - ./uploads:/app/uploads
    ports:
      - 8000:8000
    depends_on:
      -  temizlik-delisi-db
      -  temizlik-delisi-redis
  
  temizlik-delisi-redis:
    image: "redis:latest"
    container_name:  temizlik-delisi-redis
    networks:
      - main
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

  centrifugo:
    image: centrifugo/centrifugo:v5
    container_name: temizlik-delisi-centrifugo
    networks:
      - main
    volumes:
      - ./centrifugo-config.json:/centrifugo/config.json
    command: centrifugo --config=/centrifugo/config.json
    ports:
      - "8001:8000"
    restart: always
  


  # caddy:
  #   image: caddy:latest
  #   environment:
  #     - TZ="Europe/Istanbul"
  #   container_name: caddy
  #   restart: unless-stopped
  #   network_mode: "host"
  #   volumes:
  #     - ./Caddyfile:/etc/caddy/Caddyfile
  #     - ./site:/srv
  #     - caddy_data:/data
  #     - caddy_config:/config

volumes:
  temizlik_delisi_data:
  # caddy_data:
  # caddy_config:

networks:
  main:
    name: main_network
    driver: bridge
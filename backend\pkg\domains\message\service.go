package message

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/utils"
)

type Service interface {
	// Message operations
	SendMessage(ctx context.Context, senderID uuid.UUID, senderAccountType int, req *dtos.SendMessageRequest) (*dtos.MessageResponse, error)
	GetMessages(ctx context.Context, userID uuid.UUID, accountType int, req *dtos.GetMessagesRequest) ([]*dtos.MessageResponse, int64, error)
	MarkMessagesAsRead(ctx context.Context, userID uuid.UUID, accountType int, req *dtos.MarkAsReadRequest) error

	// Conversation operations
	StartConversation(ctx context.Context, userID uuid.UUID, accountType int, req *dtos.StartConversationRequest) (*dtos.ConversationResponse, error)
	GetConversations(ctx context.Context, userID uuid.UUID, accountType int, page, limit int) ([]*dtos.ConversationResponse, int64, error)
	GetConversation(ctx context.Context, userID uuid.UUID, accountType int, conversationID uuid.UUID) (*dtos.ConversationResponse, error)

	// Centrifugo operations
	GenerateConnectionToken(ctx context.Context, userID uuid.UUID) (*dtos.CentrifugoTokenResponse, error)
}

type service struct {
	repository Repository
	centrifugo *utils.CentrifugoWrapper
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
		centrifugo: utils.InitCentrifugo(),
	}
}

// SendMessage sends a new message
func (s *service) SendMessage(ctx context.Context, senderID uuid.UUID, senderAccountType int, req *dtos.SendMessageRequest) (*dtos.MessageResponse, error) {
	// Determine receiver account type (opposite of sender)
	receiverAccountType := 1 // customer
	if senderAccountType == 1 {
		receiverAccountType = 2 // cleaner
	}

	// Find or create conversation
	var conversation *entities.Conversation
	var err error

	if senderAccountType == 1 { // Customer sending to cleaner
		conversation, err = s.repository.GetConversationByParticipants(ctx, senderID, req.ReceiverID)
	} else { // Cleaner sending to customer
		conversation, err = s.repository.GetConversationByParticipants(ctx, req.ReceiverID, senderID)
	}

	if err != nil {
		// Create new conversation
		conversation = &entities.Conversation{
			Base: entities.Base{
				ID: uuid.New(),
			},
		}

		if senderAccountType == 1 { // Customer initiating
			conversation.CustomerID = senderID
			conversation.CleanerID = req.ReceiverID
		} else { // Cleaner initiating
			conversation.CustomerID = req.ReceiverID
			conversation.CleanerID = senderID
		}

		conversation.ChannelName = entities.GenerateChannelName(conversation.CustomerID, conversation.CleanerID)

		if err := s.repository.CreateConversation(ctx, conversation); err != nil {
			return nil, fmt.Errorf("failed to create conversation: %w", err)
		}
	}

	// Create message
	message := &entities.Message{
		Base: entities.Base{
			ID: uuid.New(),
		},
		SenderID:    senderID,
		ReceiverID:  req.ReceiverID,
		Content:     req.Content,
		MessageType: req.MessageType,
		FileURL:     req.FileURL,
		FileName:    req.FileName,
		ChannelName: conversation.ChannelName,
		IsRead:      false,
	}

	if message.MessageType == "" {
		message.MessageType = "text"
	}

	// Save message to database
	if err := s.repository.CreateMessage(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to create message: %w", err)
	}

	// Update conversation last message
	if err := s.repository.UpdateConversationLastMessage(ctx, conversation.ID, message.ID); err != nil {
		return nil, fmt.Errorf("failed to update conversation: %w", err)
	}

	// Get full message with sender info for response
	fullMessage, err := s.repository.GetMessageByID(ctx, message.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get message: %w", err)
	}

	// Convert to response DTO with sender info
	messageResponse := s.messageToResponseWithSender(ctx, fullMessage, senderAccountType)

	// Send via Centrifugo
	if err := s.centrifugo.SendPrivateMessage(ctx, senderID, req.ReceiverID, messageResponse); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Failed to send message via Centrifugo: %v\n", err)
	}

	// Notify both users about conversation update
	conversationResponse := s.conversationToResponse(conversation, senderID, senderAccountType)
	if err := s.centrifugo.NotifyConversationUpdate(ctx, senderID, conversationResponse); err != nil {
		fmt.Printf("Failed to notify sender about conversation update: %v\n", err)
	}

	receiverConversationResponse := s.conversationToResponse(conversation, req.ReceiverID, receiverAccountType)
	if err := s.centrifugo.NotifyConversationUpdate(ctx, req.ReceiverID, receiverConversationResponse); err != nil {
		fmt.Printf("Failed to notify receiver about conversation update: %v\n", err)
	}

	return messageResponse, nil
}

// GetMessages retrieves messages for a conversation
func (s *service) GetMessages(ctx context.Context, userID uuid.UUID, accountType int, req *dtos.GetMessagesRequest) ([]*dtos.MessageResponse, int64, error) {
	// Check if user is part of the conversation
	isInConversation, err := s.repository.IsUserInConversation(ctx, req.ConversationID, userID, accountType)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to check conversation access: %w", err)
	}

	if !isInConversation {
		return nil, 0, fmt.Errorf("user not authorized to access this conversation")
	}

	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	messages, total, err := s.repository.GetMessagesByConversation(ctx, req.ConversationID, req.Page, req.Limit)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get messages: %w", err)
	}

	// Convert to response DTOs with sender info
	var responses []*dtos.MessageResponse
	for _, message := range messages {
		// Determine sender account type based on sender ID
		senderAccountType := 1 // default to customer
		if accountType == 1 && message.SenderID != userID {
			senderAccountType = 2 // other person is cleaner
		} else if accountType == 2 && message.SenderID != userID {
			senderAccountType = 1 // other person is customer
		} else {
			senderAccountType = accountType // sender is current user
		}

		responses = append(responses, s.messageToResponseWithSender(ctx, message, senderAccountType))
	}

	return responses, total, nil
}

// MarkMessagesAsRead marks messages as read
func (s *service) MarkMessagesAsRead(ctx context.Context, userID uuid.UUID, accountType int, req *dtos.MarkAsReadRequest) error {
	// Check if user is part of the conversation
	isInConversation, err := s.repository.IsUserInConversation(ctx, req.ConversationID, userID, accountType)
	if err != nil {
		return fmt.Errorf("failed to check conversation access: %w", err)
	}

	if !isInConversation {
		return fmt.Errorf("user not authorized to access this conversation")
	}

	return s.repository.MarkMessagesAsRead(ctx, req.ConversationID, userID)
}

// StartConversation starts a new conversation
func (s *service) StartConversation(ctx context.Context, userID uuid.UUID, accountType int, req *dtos.StartConversationRequest) (*dtos.ConversationResponse, error) {
	// Determine participant roles
	var customerID, cleanerID uuid.UUID

	if accountType == 1 { // Customer starting conversation with cleaner
		customerID = userID
		cleanerID = req.ParticipantID
	} else { // Cleaner starting conversation with customer
		customerID = req.ParticipantID
		cleanerID = userID
	}

	// Check if conversation already exists
	existingConversation, err := s.repository.GetConversationByParticipants(ctx, customerID, cleanerID)
	if err == nil {
		// Conversation exists, return it
		return s.conversationToResponse(existingConversation, userID, accountType), nil
	}

	// Create new conversation
	conversation := &entities.Conversation{
		Base: entities.Base{
			ID: uuid.New(),
		},
		CustomerID:  customerID,
		CleanerID:   cleanerID,
		ChannelName: entities.GenerateChannelName(customerID, cleanerID),
	}

	if err := s.repository.CreateConversation(ctx, conversation); err != nil {
		return nil, fmt.Errorf("failed to create conversation: %w", err)
	}

	// Send initial message if provided
	if req.InitialMessage != "" {
		sendMessageReq := &dtos.SendMessageRequest{
			ReceiverID:  req.ParticipantID,
			Content:     req.InitialMessage,
			MessageType: "text",
		}

		_, err := s.SendMessage(ctx, userID, accountType, sendMessageReq)
		if err != nil {
			return nil, fmt.Errorf("failed to send initial message: %w", err)
		}
	}

	// Get updated conversation
	updatedConversation, err := s.repository.GetConversationByID(ctx, conversation.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated conversation: %w", err)
	}

	return s.conversationToResponse(updatedConversation, userID, accountType), nil
}

// GetConversations retrieves all conversations for a user
func (s *service) GetConversations(ctx context.Context, userID uuid.UUID, accountType int, page, limit int) ([]*dtos.ConversationResponse, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 20
	}

	conversations, total, err := s.repository.GetUserConversations(ctx, userID, accountType, page, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get conversations: %w", err)
	}

	// Convert to response DTOs
	var responses []*dtos.ConversationResponse
	for _, conversation := range conversations {
		response := s.conversationToResponse(conversation, userID, accountType)

		// Get unread count
		unreadCount, err := s.repository.GetUnreadMessageCount(ctx, conversation.ID, userID)
		if err == nil {
			response.UnreadCount = int(unreadCount)
		}

		responses = append(responses, response)
	}

	return responses, total, nil
}

// GetConversation retrieves a specific conversation
func (s *service) GetConversation(ctx context.Context, userID uuid.UUID, accountType int, conversationID uuid.UUID) (*dtos.ConversationResponse, error) {
	// Check if user is part of the conversation
	isInConversation, err := s.repository.IsUserInConversation(ctx, conversationID, userID, accountType)
	if err != nil {
		return nil, fmt.Errorf("failed to check conversation access: %w", err)
	}

	if !isInConversation {
		return nil, fmt.Errorf("user not authorized to access this conversation")
	}

	conversation, err := s.repository.GetConversationByID(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get conversation: %w", err)
	}

	response := s.conversationToResponse(conversation, userID, accountType)

	// Get unread count
	unreadCount, err := s.repository.GetUnreadMessageCount(ctx, conversationID, userID)
	if err == nil {
		response.UnreadCount = int(unreadCount)
	}

	return response, nil
}

// GenerateConnectionToken generates a Centrifugo connection token
func (s *service) GenerateConnectionToken(ctx context.Context, userID uuid.UUID) (*dtos.CentrifugoTokenResponse, error) {
	token, err := s.centrifugo.GenerateConnectionToken(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate connection token: %w", err)
	}

	return &dtos.CentrifugoTokenResponse{
		Token: token,
	}, nil
}

// Helper methods

// messageToResponse converts a message entity to response DTO
func (s *service) messageToResponse(message *entities.Message) *dtos.MessageResponse {
	response := &dtos.MessageResponse{
		ID:          message.ID,
		SenderID:    message.SenderID,
		ReceiverID:  message.ReceiverID,
		Content:     message.Content,
		MessageType: message.MessageType,
		FileURL:     message.FileURL,
		FileName:    message.FileName,
		IsRead:      message.IsRead,
		ReadAt:      message.ReadAt,
		ChannelName: message.ChannelName,
		CreatedAt:   message.CreatedAt,
		UpdatedAt:   message.UpdatedAt,
	}

	return response
}

// messageToResponseWithSender converts a message entity to response DTO with sender info
func (s *service) messageToResponseWithSender(ctx context.Context, message *entities.Message, senderAccountType int) *dtos.MessageResponse {
	response := s.messageToResponse(message)

	// Get sender info
	name, email, photoURL, err := s.repository.GetUserInfo(ctx, message.SenderID, senderAccountType)
	if err == nil {
		response.SenderName = name
		response.SenderEmail = email
		response.SenderPhotoURL = photoURL
	}

	return response
}

// conversationToResponse converts a conversation entity to response DTO
func (s *service) conversationToResponse(conversation *entities.Conversation, currentUserID uuid.UUID, currentUserAccountType int) *dtos.ConversationResponse {
	response := &dtos.ConversationResponse{
		ID:            conversation.ID,
		CustomerID:    conversation.CustomerID,
		CleanerID:     conversation.CleanerID,
		ChannelName:   conversation.ChannelName,
		LastMessageAt: conversation.LastMessageAt,
		CreatedAt:     conversation.CreatedAt,
		UpdatedAt:     conversation.UpdatedAt,
	}

	// Add last message if available
	if conversation.LastMessage != nil {
		// Determine last message sender account type
		lastMessageSenderAccountType := 1 // default to customer
		if conversation.LastMessage.SenderID == conversation.CleanerID {
			lastMessageSenderAccountType = 2
		}
		response.LastMessage = s.messageToResponseWithSender(context.Background(), conversation.LastMessage, lastMessageSenderAccountType)
	}

	// Add participant info (the other person in the conversation)
	var participantID uuid.UUID
	var participantAccountType int

	if currentUserAccountType == 1 { // Current user is customer, show cleaner info
		participantID = conversation.CleanerID
		participantAccountType = 2
	} else { // Current user is cleaner, show customer info
		participantID = conversation.CustomerID
		participantAccountType = 1
	}

	// Get participant info
	name, email, photoURL, err := s.repository.GetUserInfo(context.Background(), participantID, participantAccountType)
	if err == nil {
		response.ParticipantName = name
		response.ParticipantEmail = email
		response.ParticipantPhotoURL = photoURL
	}

	return response
}

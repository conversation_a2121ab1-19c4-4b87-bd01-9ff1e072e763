# Centrifugo Entegrasyonu - Temizlik Delisi

Bu dokümantasyon, Temizlik Delisi projesine Centrifugo real-time mesajlaşma entegrasyonunun nasıl çalıştığını açıklar.

## Özellikler

- Customer ve Cleaner arasında private mesajlaşma
- Real-time mesaj gönderme ve alma
- JWT tabanlı authentication
- Mesaj geçmişi ve okundu bilgisi
- Conversation yönetimi
- WebSocket bağlantısı

## Kurulum

### 1. Centrifugo Servisi Başlatma

```bash
# Docker ile Centrifugo'yu başlat
docker-compose up centrifugo -d

# Veya manuel olarak
docker run --rm -p 8001:8000 -v $(pwd)/centrifugo-config.json:/centrifugo/config.json centrifugo/centrifugo:v5 centrifugo --config=/centrifugo/config.json
```

### 2. Konfigürasyon

`config.yaml` dosyasında Centrifugo ayarları:

```yaml
centrifugo:
  url: http://localhost:8001
  api_key: your_centrifugo_api_key
  secret: your_centrifugo_secret
  token_ttl: 3600
```

## API Endpoints

### Authentication

#### Centrifugo Connection Token Al
```
GET /api/v1/centrifugo/token
Authorization: Bearer <jwt_token>
```

### Mesajlaşma

#### Mesaj Gönder
```
POST /api/v1/message/send
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "receiver_id": "uuid",
  "content": "Merhaba!",
  "message_type": "text"
}
```

#### Mesajları Listele
```
GET /api/v1/message?conversation_id=uuid&page=1&limit=20
Authorization: Bearer <jwt_token>
```

#### Mesajları Okundu Olarak İşaretle
```
POST /api/v1/message/read
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "conversation_id": "uuid"
}
```

### Conversation Yönetimi

#### Yeni Conversation Başlat
```
POST /api/v1/conversation/start
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "participant_id": "uuid",
  "initial_message": "Merhaba, hizmetiniz hakkında bilgi alabilir miyim?"
}
```

#### Conversation'ları Listele
```
GET /api/v1/conversation?page=1&limit=20
Authorization: Bearer <jwt_token>
```

#### Belirli Conversation'ı Getir
```
GET /api/v1/conversation/{id}
Authorization: Bearer <jwt_token>
```

## WebSocket Bağlantısı

### 1. Connection Token Al

```javascript
const response = await fetch('/api/v1/centrifugo/token', {
  headers: {
    'Authorization': 'Bearer ' + userJwtToken
  }
});
const { token } = await response.json();
```

### 2. Centrifugo'ya Bağlan

```javascript
import { Centrifuge } from 'centrifuge';

const centrifuge = new Centrifuge('ws://localhost:8001/connection/websocket', {
  token: token
});

centrifuge.on('connecting', function(ctx) {
  console.log('Connecting...', ctx);
});

centrifuge.on('connected', function(ctx) {
  console.log('Connected', ctx);
});

centrifuge.connect();
```

### 3. Channel'lara Subscribe Ol

```javascript
// Personal channel - genel bildirimler için
const personalSub = centrifuge.newSubscription(`personal:${userId}`);

personalSub.on('publication', function(ctx) {
  console.log('Personal notification:', ctx.data);
});

personalSub.subscribe();

// Private conversation channel
const conversationSub = centrifuge.newSubscription(`private:${userId1}:${userId2}`);

conversationSub.on('publication', function(ctx) {
  console.log('New message:', ctx.data);
  // Yeni mesajı UI'da göster
});

conversationSub.subscribe();
```

## Channel Yapısı

### Personal Channels
- Format: `personal:{user_id}`
- Kullanım: Conversation güncellemeleri, genel bildirimler
- Sadece ilgili kullanıcı subscribe olabilir

### Private Conversation Channels
- Format: `private:{user_id_1}:{user_id_2}`
- Kullanım: İki kullanıcı arasında private mesajlaşma
- Sadece conversation'daki kullanıcılar subscribe olabilir
- User ID'ler alfabetik sıraya göre düzenlenir

## Mesaj Formatları

### Yeni Mesaj
```json
{
  "type": "message",
  "event": "new_message",
  "message": {
    "id": "uuid",
    "sender_id": "uuid",
    "receiver_id": "uuid",
    "content": "Mesaj içeriği",
    "message_type": "text",
    "created_at": "2023-01-01T12:00:00Z",
    "sender_name": "John Doe",
    "sender_email": "<EMAIL>"
  }
}
```

### Conversation Güncelleme
```json
{
  "type": "conversation_update",
  "event": "conversation_updated",
  "conversation": {
    "id": "uuid",
    "channel_name": "private:uuid1:uuid2",
    "last_message": {...},
    "unread_count": 3,
    "participant_name": "Jane Smith"
  }
}
```

## Güvenlik

- Tüm WebSocket bağlantıları JWT token ile doğrulanır
- Channel subscription'ları server-side doğrulanır
- Kullanıcılar sadece yetkili oldukları channel'lara subscribe olabilir
- Direct publish client'lardan engellenir, tüm mesajlar API üzerinden gönderilir

## Test

### 1. Servisleri Başlat
```bash
docker-compose up -d
```

### 2. API Test
```bash
# Connection token al
curl -H "Authorization: Bearer <jwt>" http://localhost:8000/api/v1/centrifugo/token

# Mesaj gönder
curl -X POST -H "Authorization: Bearer <jwt>" -H "Content-Type: application/json" \
  -d '{"receiver_id":"uuid","content":"Test mesajı"}' \
  http://localhost:8000/api/v1/message/send
```

### 3. WebSocket Test
Browser console'da yukarıdaki JavaScript kodlarını kullanarak test edebilirsiniz.

## Troubleshooting

### Centrifugo Bağlantı Sorunları
- Centrifugo servisinin çalıştığından emin olun: `docker ps | grep centrifugo`
- Config dosyasının doğru mount edildiğini kontrol edin
- API key ve secret'ların config'de doğru olduğunu kontrol edin

### Authentication Sorunları
- JWT token'ın geçerli olduğunu kontrol edin
- Token'ın doğru header'da gönderildiğini kontrol edin
- Centrifugo config'deki proxy endpoint'lerin doğru olduğunu kontrol edin

### Channel Subscription Sorunları
- User ID'lerin doğru olduğunu kontrol edin
- Channel name formatının doğru olduğunu kontrol edin
- Kullanıcının channel'a subscribe olma yetkisi olduğunu kontrol edin

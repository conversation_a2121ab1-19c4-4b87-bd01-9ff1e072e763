package server

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"path"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/temizlik-delisi/app/api/routes"
	admin_routes "github.com/temizlik-delisi/app/api/routes/admin"
	centrifugo_routes "github.com/temizlik-delisi/app/api/routes/centrifugo"
	cleaner_routes "github.com/temizlik-delisi/app/api/routes/cleaner"
	common_routes "github.com/temizlik-delisi/app/api/routes/common"
	customer_routes "github.com/temizlik-delisi/app/api/routes/customer"
	message_routes "github.com/temizlik-delisi/app/api/routes/message"
	"github.com/temizlik-delisi/docs"
	"github.com/temizlik-delisi/pkg/config"
	"github.com/temizlik-delisi/pkg/database"
	"github.com/temizlik-delisi/pkg/domains/address"
	"github.com/temizlik-delisi/pkg/domains/admin"
	"github.com/temizlik-delisi/pkg/domains/blog"
	"github.com/temizlik-delisi/pkg/domains/cleaner"
	"github.com/temizlik-delisi/pkg/domains/comment"
	"github.com/temizlik-delisi/pkg/domains/license"
	"github.com/temizlik-delisi/pkg/domains/message"
	"github.com/temizlik-delisi/pkg/domains/offer"
	"github.com/temizlik-delisi/pkg/domains/order"
	"github.com/temizlik-delisi/pkg/domains/profil"
	"github.com/temizlik-delisi/pkg/domains/serviceCategory"

	"github.com/temizlik-delisi/pkg/domains/auth"
	"github.com/temizlik-delisi/pkg/domains/customerPreference"
	"github.com/temizlik-delisi/pkg/domains/verification"

	"github.com/temizlik-delisi/pkg/domains/version"
	"github.com/temizlik-delisi/pkg/embed"
	"github.com/temizlik-delisi/pkg/middleware"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())

	//app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/docs/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()

	// -----> Routes Start
	api := app.Group("/api/v1")

	version_repo := version.NewRepo(db)
	version_service := version.NewService(version_repo)
	routes.VersionRoutes(api, version_service)

	auth_repo := auth.NewRepo(db)
	auth_service := auth.NewService(auth_repo)
	routes.AuthRoutes(api, auth_service)

	admin_repo := admin.NewRepo(db)
	admin_service := admin.NewService(admin_repo)
	admin_routes.AdminRoutes(api, admin_service)

	address_repo := address.NewRepo(db)
	address_service := address.NewService(address_repo)

	customer_preference_repo := customerPreference.NewRepo(db)
	customer_preference_service := customerPreference.NewService(customer_preference_repo)

	verification_repo := verification.NewRepo(db)
	verification_service := verification.NewService(verification_repo)

	blog_repo := blog.NewRepo(db)
	blog_service := blog.NewService(blog_repo)

	order_repo := order.NewRepo(db)
	order_service := order.NewService(order_repo)

	license_repo := license.NewRepo(db)
	license_service := license.NewService(license_repo)

	profil_repo := profil.NewRepo(db)
	profil_service := profil.NewService(profil_repo)

	cleaner_repo := cleaner.NewRepo(db)
	cleaner_service := cleaner.NewService(cleaner_repo)

	service_category_repo := serviceCategory.NewRepo(db)
	service_category_service := serviceCategory.NewService(service_category_repo)

	comment_repo := comment.NewRepo(db)
	comment_service := comment.NewService(comment_repo)

	offer_repo := offer.NewRepo(db)
	offer_service := offer.NewService(offer_repo)

	message_repo := message.NewRepo(db)
	message_service := message.NewService(message_repo)

	common_routes.CommonRoutes(api, blog_service, order_service, address_service, license_service, profil_service, service_category_service, comment_service, offer_service)
	customer_routes.CustomerRoutes(api, customer_preference_service)
	cleaner_routes.CleanerRoutes(api, cleaner_service, verification_service)
	message_routes.MessageRoutes(api, message_service)
	centrifugo_routes.CentrifugoRoutes(api)

	// Routes End <-----

	app.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "docs/index.html")
	})

	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "temizlik-delisi"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "temizlik-delisi"
	}

	docs.SwaggerInfo.Host = config.InitConfig().App.BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/docs/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	// Serve uploaded files statically
	app.Static("/uploads", "./uploads")

	app.GET("/assets/*filepath", func(c *gin.Context) {
		c.FileFromFS(path.Join("/dist/", c.Request.URL.Path), http.FS(embed.StaticsFS()))
	})
	app.Any("/", func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})
	app.GET("/robots.txt", func(c *gin.Context) {
		c.FileFromFS("dist/robots.txt", http.FS(embed.StaticsFS()))
	})
	app.GET("/favicon.ico", func(c *gin.Context) {
		c.FileFromFS("dist/favicon.ico", http.FS(embed.StaticsFS()))
	})
	app.NoRoute(func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}

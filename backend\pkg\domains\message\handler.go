package message

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/temizlik-delisi/pkg/dtos"
	"github.com/temizlik-delisi/pkg/entities"
	"github.com/temizlik-delisi/pkg/localizer"
	"github.com/temizlik-delisi/pkg/mainlog"
	"github.com/temizlik-delisi/pkg/state"
)

// @Summary Send Message
// @Description Send a message to another user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.SendMessageRequest true "Send message request"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/message/send [POST]
func SendMessage(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.SendMessageRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Send Message Bind JSON Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: state.GetCurrentID(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		userID := state.GetCurrentID(c)
		accountType := state.GetCurrentAccountType(c)

		response, err := s.SendMessage(c, userID, accountType, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Send Message Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   response,
			"status": http.StatusOK,
		})
	}
}

// @Summary Get Messages
// @Description Get messages for a conversation
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param conversation_id query string true "Conversation ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/message [GET]
func GetMessages(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetMessagesRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		userID := state.GetCurrentID(c)
		accountType := state.GetCurrentAccountType(c)

		messages, total, err := s.GetMessages(c, userID, accountType, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Messages Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": gin.H{
				"messages": messages,
				"total":    total,
				"page":     req.Page,
				"limit":    req.Limit,
			},
			"status": http.StatusOK,
		})
	}
}

// @Summary Mark Messages as Read
// @Description Mark messages in a conversation as read
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.MarkAsReadRequest true "Mark as read request"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/message/read [POST]
func MarkMessagesAsRead(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.MarkAsReadRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		userID := state.GetCurrentID(c)
		accountType := state.GetCurrentAccountType(c)

		err := s.MarkMessagesAsRead(c, userID, accountType, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Mark Messages as Read Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Messages marked as read",
			"status":  http.StatusOK,
		})
	}
}

// @Summary Start Conversation
// @Description Start a new conversation with another user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param payload body dtos.StartConversationRequest true "Start conversation request"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/conversation/start [POST]
func StartConversation(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.StartConversationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		userID := state.GetCurrentID(c)
		accountType := state.GetCurrentAccountType(c)

		conversation, err := s.StartConversation(c, userID, accountType, &req)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Start Conversation Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   conversation,
			"status": http.StatusOK,
		})
	}
}

// @Summary Get Conversations
// @Description Get all conversations for the current user
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/conversation [GET]
func GetConversations(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

		userID := state.GetCurrentID(c)
		accountType := state.GetCurrentAccountType(c)

		conversations, total, err := s.GetConversations(c, userID, accountType, page, limit)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Conversations Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": gin.H{
				"conversations": conversations,
				"total":         total,
				"page":          page,
				"limit":         limit,
			},
			"status": http.StatusOK,
		})
	}
}

// @Summary Get Conversation
// @Description Get a specific conversation by ID
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path string true "Conversation ID"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 404 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/conversation/{id} [GET]
func GetConversation(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		conversationIDStr := c.Param("id")
		conversationID, err := uuid.Parse(conversationIDStr)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  localizer.GetTranslated("error_request", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusBadRequest,
			})
			return
		}

		userID := state.GetCurrentID(c)
		accountType := state.GetCurrentAccountType(c)

		conversation, err := s.GetConversation(c, userID, accountType, conversationID)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Conversation Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   conversation,
			"status": http.StatusOK,
		})
	}
}

// @Summary Get Centrifugo Connection Token
// @Description Get a JWT token for Centrifugo WebSocket connection
// @Tags Message
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} map[string]any
// @Failure 401 {object} map[string]any
// @Failure 500 {object} map[string]any
// @Router /common/centrifugo/token [GET]
func GetCentrifugoToken(s Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := state.GetCurrentID(c)

		tokenResponse, err := s.GenerateConnectionToken(c, userID)
		if err != nil {
			mainlog.CreateLog(&entities.Log{
				Title:     "Get Centrifugo Token Error",
				Message:   "Error: " + err.Error(),
				Type:      "error",
				Proto:     "http",
				Ip:        state.GetCurrentUserIP(c),
				Url:       c.Request.URL.Path,
				OS:        state.GetCurrentOS(c),
				AccountID: userID,
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":  localizer.GetTranslated("error_general", state.GetCurrentPhoneLanguage(c), nil),
				"status": http.StatusInternalServerError,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   tokenResponse,
			"status": http.StatusOK,
		})
	}
}

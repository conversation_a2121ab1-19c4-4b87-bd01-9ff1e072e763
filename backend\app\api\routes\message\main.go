package message

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/domains/message"
	"github.com/temizlik-delisi/pkg/middleware"
)

func MessageRoutes(r *gin.RouterGroup, s message.Service) {
	m := r.Group("/message")
	c := r.Group("/conversation")
	centrifugo := r.Group("/centrifugo")

	// Apply middleware for authentication and account type validation
	m.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	c.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	centrifugo.Use(middleware.FromClient(), middleware.Authorized(), middleware.AccountType(1, 2))
	{
		// Message endpoints
		m.POST("/send", message.SendMessage(s))
		m.GET("", message.GetMessages(s))
		m.POST("/read", message.MarkMessagesAsRead(s))

		// Conversation endpoints
		c.POST("/start", message.StartConversation(s))
		c.GET("", message.GetConversations(s))
		c.GET("/:id", message.GetConversation(s))

		// Centrifugo endpoints
		centrifugo.GET("/token", message.GetCentrifugoToken(s))
	}
}

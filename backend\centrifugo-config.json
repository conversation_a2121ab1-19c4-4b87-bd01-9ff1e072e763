{"token_hmac_secret_key": "your_centrifugo_secret", "api_key": "your_centrifugo_api_key", "admin": true, "admin_password": "admin_password", "admin_secret": "admin_secret", "allowed_origins": ["*"], "publish": true, "subscribe_to_publish": true, "anonymous": false, "presence": true, "join_leave": true, "history_size": 10, "history_ttl": "300s", "force_push_join_leave": false, "user_subscribe_to_personal": true, "client_anonymous": false, "proxy_connect_endpoint": "http://host.docker.internal:8000/api/v1/centrifugo/auth", "proxy_connect_timeout": "1s", "proxy_subscribe_endpoint": "http://host.docker.internal:8000/api/v1/centrifugo/subscribe", "proxy_subscribe_timeout": "1s", "proxy_publish_endpoint": "http://host.docker.internal:8000/api/v1/centrifugo/publish", "proxy_publish_timeout": "1s", "namespaces": [{"name": "private", "publish": true, "subscribe_to_publish": true, "anonymous": false, "presence": true, "join_leave": true, "history_size": 50, "history_ttl": "300s", "force_push_join_leave": false}, {"name": "personal", "publish": true, "subscribe_to_publish": true, "anonymous": false, "presence": true, "join_leave": true, "history_size": 10, "history_ttl": "300s", "force_push_join_leave": false}]}
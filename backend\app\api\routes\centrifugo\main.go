package centrifugo

import (
	"github.com/gin-gonic/gin"
	"github.com/temizlik-delisi/pkg/middleware"
)

func CentrifugoRoutes(r *gin.RouterGroup) {
	centrifugo := r.Group("/centrifugo")
	
	// Webhook endpoints for Centrifugo
	centrifugo.Use(middleware.CentrifugoWebhookAuth())
	{
		centrifugo.POST("/auth", middleware.CentrifugoAuth())
		centrifugo.POST("/subscribe", middleware.CentrifugoSubscribe())
		centrifugo.POST("/publish", middleware.CentrifugoPublish())
	}
}

package entities

import (
	"time"

	"github.com/google/uuid"
)

type Message struct {
	Base
	SenderID    uuid.UUID  `json:"sender_id" gorm:"type:uuid;not null;index"`
	ReceiverID  uuid.UUID  `json:"receiver_id" gorm:"type:uuid;not null;index"`
	Content     string     `json:"content" gorm:"type:text;not null"`
	MessageType string     `json:"message_type" gorm:"default:'text'"` // text, image, file
	FileURL     string     `json:"file_url,omitempty"`
	FileName    string     `json:"file_name,omitempty"`
	IsRead      bool       `json:"is_read" gorm:"default:false"`
	ReadAt      *time.Time `json:"read_at,omitempty"`
	ChannelName string     `json:"channel_name" gorm:"index"` // private channel name for Centrifugo

	// Relations - Note: We use interface{} here because sender/receiver can be either Customer or Cleaner
	// The actual relationship will be determined by the account type in the business logic
}

// Conversation represents a conversation between two users
type Conversation struct {
	Base
	CustomerID    uuid.UUID  `json:"customer_id" gorm:"type:uuid;not null;index"`
	CleanerID     uuid.UUID  `json:"cleaner_id" gorm:"type:uuid;not null;index"`
	ChannelName   string     `json:"channel_name" gorm:"uniqueIndex"` // unique channel name
	LastMessageID *uuid.UUID `json:"last_message_id,omitempty" gorm:"type:uuid"`
	LastMessageAt *time.Time `json:"last_message_at,omitempty"`

	// Relations
	Customer    *Customer `json:"customer,omitempty" gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE"`
	Cleaner     *Cleaner  `json:"cleaner,omitempty" gorm:"foreignKey:CleanerID;constraint:OnDelete:CASCADE"`
	LastMessage *Message  `json:"last_message,omitempty" gorm:"foreignKey:LastMessageID;constraint:OnDelete:SET NULL"`
}

// GenerateChannelName creates a unique channel name for private conversation
func GenerateChannelName(customerID, cleanerID uuid.UUID) string {
	// Sort IDs to ensure consistent channel names regardless of who initiates
	if customerID.String() < cleanerID.String() {
		return "private:" + customerID.String() + ":" + cleanerID.String()
	}
	return "private:" + cleanerID.String() + ":" + customerID.String()
}

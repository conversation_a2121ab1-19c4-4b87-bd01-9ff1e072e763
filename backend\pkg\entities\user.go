package entities

import (
	"time"
)

type BaseInfo struct {
	Name                   string    `json:"name"`
	Surname                string    `json:"surname"`
	Email                  string    `json:"email" gorm:"uniqueIndex"`
	Password               *string   `json:"password,omitempty"` // Nullable for OAuth users
	GoogleID               *string   `json:"google_id,omitempty" gorm:"uniqueIndex"`
	AppleID                *string   `json:"apple_id,omitempty" gorm:"uniqueIndex"`
	Provider               string    `json:"provider" gorm:"default:'local'"` // 'local', 'google', 'apple'
	TimeZone               string    `json:"time_zone" gorm:"default:'Europe/Istanbul'"`
	PhoneLanguage          string    `json:"phone_language" gorm:"default:'tr'"`
	Os                     string    `json:"os"`
	PurchaseID             string    `json:"purchase_id"`
	PushNotifToken         string    `json:"push_notif_token"`
	ReferenceID            string    `json:"reference_id" example:"ABC45678"`
	LastVersionName        string    `json:"last_version_name"`
	LastVersionBuildNumber int       `json:"last_version_build_number"`
	LastLoginDate          time.Time `json:"last_login_date"`
}

type BaseDetail struct {
	DateOfBirth      string  `json:"date_of_birth"`
	Phone            string  `json:"phone"`
	Country          string  `json:"country"`
	ProfilePhotoURL  string  `json:"profile_photo_url"`
	IsProfileUpdated bool    `json:"is_profile_updated" gorm:"default:false"`
	TotalPoint       int     `json:"total_point" gorm:"default:0"`
	TotalComment     int     `json:"total_rate" gorm:"default:0"`
	AverageRate      float64 `json:"average_rate" gorm:"default:0"`
}

type Customer struct {
	Base
	BaseInfo
	BaseDetail

	// Join relationships
	CustomerPreferences []CustomerPreference `json:"customer_preferences,omitempty" gorm:"foreignKey:CustomerID;references:ID"`
}

type Cleaner struct {
	Base
	BaseInfo
	BaseDetail

	TCNo                           string `json:"tc_no" example:"**********1"`
	TCNoVerified                   bool   `json:"tc_no_verified" example:"false"`
	CriminalRecordDocumentURL      string `json:"criminal_record_document_url" example:"https://minio.example.com/criminal-records/document.pdf"`
	CriminalRecordDocumentFilename string `json:"criminal_record_document_filename" example:"document.pdf"`
	CriminalRecordDocumentVerified bool   `json:"criminal_record_document_verified" example:"false"`
}

type UserResponse struct {
	AccountID       string `json:"account_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType     string `json:"account_type" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name            string `json:"name" example:"John"`
	Surname         string `json:"surname" example:"Doe"`
	Email           string `json:"email" example:"<EMAIL>"`
	ProfilePhotoURL string `json:"profile_photo_url" example:"https://minio.example.com/profile-photos/photo.jpg"`
}

type ResponseForDetail struct {
	ID                     string    `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountType            int       `json:"account_type" example:"1"`
	CreatedAt              string    `json:"created_at" example:"2021-01-01 00:00:00"`
	Name                   string    `json:"name" example:"John"`
	Surname                string    `json:"surname" example:"Doe"`
	Email                  string    `json:"email" example:"<EMAIL>"`
	GoogleID               *string   `json:"google_id,omitempty" example:"**********"`
	AppleID                *string   `json:"apple_id,omitempty" example:"**********"`
	Provider               string    `json:"provider" example:"google"`
	TimeZone               string    `json:"time_zone" example:"Europe/Istanbul"`
	PhoneLanguage          string    `json:"phone_language" example:"tr"`
	Os                     string    `json:"os" example:"android"`
	PurchaseID             string    `json:"purchase_id" example:"**********"`
	PushNotifToken         string    `json:"push_notif_token" example:"**********"`
	ReferenceID            string    `json:"reference_id" example:"ABC45678"`
	LastVersionName        string    `json:"last_version_name" example:"1.0.0"`
	LastVersionBuildNumber int       `json:"last_version_build_number" example:"3"`
	LastLoginDate          time.Time `json:"last_login_date" example:"2021-01-01 00:00:00"`

	DateOfBirth      string `json:"date_of_birth" example:"1990-01-01"`
	Phone            string `json:"phone" example:"**********"`
	Country          string `json:"country" example:"Turkey"`
	ProfilePhotoURL  string `json:"profile_photo_url" example:"https://minio.example.com/profile-photos/photo.jpg"`
	IsProfileUpdated bool   `json:"is_profile_updated" example:"true"`

	TotalPoint   int     `json:"total_point" example:"10"`
	TotalComment int     `json:"total_comment" example:"5"`
	AverageRate  float64 `json:"average_rate" example:"2.0"`
}

func (c *Customer) ResponseForDetailCustomer() ResponseForDetail {
	var resp ResponseForDetail

	resp.ID = c.ID.String()
	resp.AccountType = 1
	resp.CreatedAt = c.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Name = c.Name
	resp.Surname = c.Surname
	resp.Email = c.Email
	resp.GoogleID = c.GoogleID
	resp.AppleID = c.AppleID
	resp.Provider = c.Provider
	resp.TimeZone = c.TimeZone

	resp.PhoneLanguage = c.PhoneLanguage
	resp.Os = c.Os
	resp.PurchaseID = c.PurchaseID
	resp.PushNotifToken = c.PushNotifToken
	resp.ReferenceID = c.ReferenceID
	resp.LastVersionName = c.LastVersionName
	resp.LastVersionBuildNumber = c.LastVersionBuildNumber
	resp.LastLoginDate = c.LastLoginDate

	resp.DateOfBirth = c.DateOfBirth
	resp.Phone = c.Phone

	resp.Country = c.Country
	resp.ProfilePhotoURL = c.ProfilePhotoURL
	resp.IsProfileUpdated = c.IsProfileUpdated

	resp.TotalPoint = c.TotalPoint
	resp.TotalComment = c.TotalComment
	resp.AverageRate = c.AverageRate

	return resp
}

func (c *Cleaner) ResponseForDetailCleaner() ResponseForDetail {
	var resp ResponseForDetail

	resp.ID = c.ID.String()
	resp.AccountType = 2
	resp.CreatedAt = c.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Name = c.Name
	resp.Surname = c.Surname
	resp.Email = c.Email
	resp.GoogleID = c.GoogleID
	resp.AppleID = c.AppleID
	resp.Provider = c.Provider
	resp.TimeZone = c.TimeZone

	resp.PhoneLanguage = c.PhoneLanguage
	resp.Os = c.Os
	resp.PurchaseID = c.PurchaseID
	resp.PushNotifToken = c.PushNotifToken

	resp.ReferenceID = c.ReferenceID
	resp.LastVersionName = c.LastVersionName
	resp.LastVersionBuildNumber = c.LastVersionBuildNumber
	resp.LastLoginDate = c.LastLoginDate

	resp.DateOfBirth = c.DateOfBirth
	resp.Phone = c.Phone

	resp.Country = c.Country
	resp.ProfilePhotoURL = c.ProfilePhotoURL
	resp.IsProfileUpdated = c.IsProfileUpdated

	resp.TotalPoint = c.TotalPoint
	resp.TotalComment = c.TotalComment
	resp.AverageRate = c.AverageRate

	return resp
}
